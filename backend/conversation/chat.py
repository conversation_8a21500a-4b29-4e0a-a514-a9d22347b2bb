from backend.services.auth_service import auth_service

class ChatService:
    """Chat service class"""
    async def list_conversations(self, user_id: int):
        try:
            await auth_service.connect_db()
            conversations = await auth_service.db.conversation.find_many(
                where={"users": {"some": {"id": user_id}}},
                include={"users": True, "messages": True}
            )
            return conversations
        except Exception as e:
            print(f"Error listing conversations: {str(e)}")
            return []
        finally:
            await auth_service.disconnect_db()

    async def create_conversation(self, user_ids: list[int]):
        try:
            await auth_service.connect_db()
            conversation = await auth_service.db.conversation.create(
                data={
                    "users": {"connect": [{"id": user_id} for user_id in user_ids]}
                }
            )
            return conversation
        except Exception as e:
            print(f"Error creating conversation: {str(e)}")
            return None
        finally:
            await auth_service.disconnect_db()

    async def get_conversation(self, conversation_id: int):
        try:
            await auth_service.connect_db()
            conversation = await auth_service.db.conversation.find_unique(
                where={"id": conversation_id},
                include={"users": True, "messages": True}
            )
            return conversation
        except Exception as e:
            print(f"Error getting conversation: {str(e)}")
            return None
        finally:
            await auth_service.disconnect_db()

    async def get_conversations(self, user_id: int):
        try:
            await auth_service.connect_db()
            conversations = await auth_service.db.conversation.find_many(
                where={"users": {"some": {"id": user_id}}},
                include={"users": True, "messages": True}
            )
            return conversations
        except Exception as e:
            print(f"Error getting conversations: {str(e)}")
            return []
        finally:
            await auth_service.disconnect_db()
